<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Predicto</title>
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>ADMIN <span class="accent">CONTROL</span></h1>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link active">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="matches.html" class="nav-link">Matches</a>
                    </li>
                    <li class="nav-item">
                        <a href="users.html" class="nav-link">Users</a>
                    </li>
                    <li class="nav-item">
                        <a href="stats.html" class="nav-link">Statistics</a>
                    </li>
                </ul>
                <div class="admin-info">
                    <span id="admin-status">Admin Panel</span>
                    <button id="logout-btn" class="btn btn-danger btn-sm">Logout</button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Admin Login</h2>
            </div>
            <div class="modal-body">
                <div class="login-form">
                    <div class="input-group">
                        <label for="admin-username">Username:</label>
                        <input type="text" id="admin-username" placeholder="Enter username" value="admin">
                    </div>
                    <div class="input-group">
                        <label for="admin-password">Password:</label>
                        <input type="password" id="admin-password" placeholder="Enter password">
                    </div>
                    <button id="login-btn" class="btn btn-primary">Login</button>
                    <div id="login-error" class="error" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <main class="main-content" id="admin-content" style="display: none;">
        <div class="container">
            <section class="dashboard-header">
                <h1>ADMIN <span class="accent">CONTROL</span></h1>
                <p>Command Center for Tournament Operations</p>
            </section>

            <div class="dashboard-grid">
                <!-- Create Match Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus-circle"></i> Create New Match</h3>
                    </div>
                    <div class="card-body">
                        <form id="create-match-form">
                            <div class="input-group">
                                <label for="team-a">Team A:</label>
                                <input type="text" id="team-a" placeholder="Enter team name" required>
                            </div>
                            <div class="input-group">
                                <label for="team-b">Team B:</label>
                                <input type="text" id="team-b" placeholder="Enter team name" required>
                            </div>
                            <div class="input-group">
                                <label for="prediction-duration">Prediction Window (minutes):</label>
                                <input type="number" id="prediction-duration" value="5" min="1" max="60" required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Match
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <button id="refresh-matches" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh Matches
                            </button>
                            <button id="view-leaderboard" class="btn btn-info">
                                <i class="fas fa-trophy"></i> View Leaderboard
                            </button>
                            <a href="../frontend/index.html" class="btn btn-success" target="_blank">
                                <i class="fas fa-external-link-alt"></i> View Public Site
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Matches Card -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Recent Matches</h3>
                        <button id="load-matches" class="btn btn-primary btn-sm">
                            <i class="fas fa-refresh"></i> Load
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="loading-matches" class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading matches...</span>
                        </div>
                        <div id="matches-container">
                            <!-- Matches will be loaded here -->
                        </div>
                        <div id="no-matches" class="no-data" style="display: none;">
                            <i class="fas fa-calendar-times"></i>
                            <p>No matches found. Create your first match above!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Predictions - Admin Panel</p>
        </div>
    </footer>

    <script src="js/admin.js"></script>
</body>
</html>
