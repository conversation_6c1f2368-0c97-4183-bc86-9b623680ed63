# Deploying the Backend to Vercel

This guide explains how to deploy the Match Prediction backend to Vercel.

## Prerequisites

1. A Vercel account (sign up at [vercel.com](https://vercel.com))
2. Vercel CLI installed (optional, for local testing)
   ```
   npm install -g vercel
   ```

## Deployment Steps

### 1. Push Your Code to GitHub

First, make sure your code is in a GitHub repository.

### 2. Connect to Vercel

1. Go to [vercel.com](https://vercel.com) and log in
2. Click "Add New" > "Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Other
   - Root Directory: `backend` (if your backend is in a subdirectory)
   - Build Command: Leave empty (handled by vercel.json)
   - Output Directory: Leave empty

### 3. Environment Variables

Add the following environment variables in the Vercel project settings:

- `MONGODB_URI`: Your MongoDB connection string
- `DB_NAME`: Database name (e.g., valorant_predictions)
- `SECRET_KEY`: A secure random string for Flask sessions
- `ADMIN_USERNAME`: Admin username
- `ADMIN_PASSWORD`: Admin password

### 4. Deploy

Click "Deploy" and wait for the deployment to complete.

## Local Testing with Vercel CLI

To test the deployment locally before pushing to production:

```bash
cd backend
vercel dev
```

This will start a local development server that mimics the Vercel environment.

## Troubleshooting

- If you encounter errors, check the Vercel deployment logs
- Ensure all required environment variables are set
- Verify that your MongoDB instance is accessible from Vercel's servers

## Important Notes

- Vercel uses a serverless architecture, so the Flask app runs in a stateless environment
- Long-running processes or background tasks may not work as expected
- The free tier has limitations on execution time and resources